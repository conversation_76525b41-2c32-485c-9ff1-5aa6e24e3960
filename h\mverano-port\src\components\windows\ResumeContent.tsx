
import { Download } from 'lucide-react';
import { useRef, useCallback } from 'react';

interface ResumeContentProps {
  updateWindowSize?: (windowId: string, size: { width: number; height: number }) => void;
  windowId?: string;
}

const ResumeContent = ({ updateWindowSize, windowId }: ResumeContentProps) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const handleIframeLoad = useCallback(() => {
    // Trigger a subtle window resize to fix PDF zoom issues
    if (updateWindowSize && windowId && iframeRef.current) {
      // Use requestAnimationFrame to ensure DOM is ready
      requestAnimationFrame(() => {
        // Add a small delay to allow iframe content to fully render
        setTimeout(() => {
          if (updateWindowSize && windowId && iframeRef.current) {
            // Get the current window element to determine its current size
            const windowElement = iframeRef.current.closest('.xp-window') as HTMLElement;
            if (windowElement) {
              const currentWidth = windowElement.offsetWidth;
              const currentHeight = windowElement.offsetHeight;

              // Ensure we have valid dimensions before proceeding
              if (currentWidth > 0 && currentHeight > 0) {
                // Trigger a minimal resize (increase by 2 pixels) - imperceptible to user
                updateWindowSize(windowId, {
                  width: currentWidth + 2,
                  height: currentHeight + 2
                });

                // Immediately revert back to original size after a brief moment
                setTimeout(() => {
                  if (updateWindowSize && windowId) {
                    updateWindowSize(windowId, {
                      width: currentWidth,
                      height: currentHeight
                    });
                  }
                }, 100); // Brief delay to ensure the resize triggers layout recalculation

                // Additional fallback: try again after a longer delay if needed
                setTimeout(() => {
                  if (updateWindowSize && windowId && iframeRef.current) {
                    // Check if we still need to trigger another resize
                    const iframe = iframeRef.current;
                    if (iframe.offsetHeight > 0 && iframe.offsetWidth > 0) {
                      // Trigger one more subtle resize cycle as a fallback
                      updateWindowSize(windowId, {
                        width: currentWidth + 1,
                        height: currentHeight + 1
                      });
                      setTimeout(() => {
                        if (updateWindowSize && windowId) {
                          updateWindowSize(windowId, {
                            width: currentWidth,
                            height: currentHeight
                          });
                        }
                      }, 50);
                    }
                  }
                }, 500); // Longer delay for fallback
              }
            }
          }
        }, 200); // Slightly longer delay to ensure PDF content is ready
      });
    }
  }, [updateWindowSize, windowId]);

  return (
    <div className="h-full flex flex-col bg-gray-100 font-tahoma text-xs">
      <div className="p-1 bg-gray-200 border-b border-gray-300 flex items-center space-x-1 shrink-0">
        <span className="text-gray-500 font-semibold px-2">Address</span>
        <div className="flex-1 bg-white border border-gray-400 p-0.5 flex items-center">
          <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACLSURBVDhPY2AY6OLiAjo5Of+P4CIgvwHif4z/DAyM6Asa3AyMDCysDEyWpCAg/w9iDAwMjAycgCof4x8YGBgYWBgYjAkZGBgYGFgYWEzAGBj8//9/BgaE/zMy/P9/YGAQZWBgYGD4//+fmYGBgYFB5f/l/7GNgYGBgYHh//n/dUYGBgYGDADGABo4iP2XAAAAAElFTkSuQmCC" alt="ie icon" className="h-4 w-4 mr-1" />
          <input type="text" readOnly value="file:///C:/Users/<USER>/Documents/my_resume.pdf" className="w-full bg-transparent outline-none" />
        </div>
        <button className="xp-button px-2">Go</button>
      </div>
      <div className="flex-1 bg-white overflow-hidden">
        <iframe
          ref={iframeRef}
          src="https://drive.google.com/file/d/1XaY0tfH38EOgMk20P-qi3wWz_ZqGTi64/preview?rm=minimal&embedded=true"
          className="w-full h-full"
          style={{ border: 0 }}
          onLoad={handleIframeLoad}
        ></iframe>
      </div>
      <div className="p-2 bg-gray-100 border-t border-gray-300 flex justify-end items-center shrink-0">
        <div className="flex items-center space-x-2">
          <a href="https://drive.google.com/uc?export=download&id=1XaY0tfH38EOgMk20P-qi3wWz_ZqGTi64" download="my_resume.pdf" target="_blank" className="xp-button px-4 py-1 flex items-center space-x-2 no-underline text-black">
            <Download size={16} />
            <span>Download Resume</span>
          </a>
        </div>
      </div>
    </div>
  );
};

export default ResumeContent;
