
import { Download } from 'lucide-react';
import { useRef, useCallback } from 'react';

const ResumeContent = () => {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const handleIframeLoad = useCallback(() => {
    // Force layout recalculation after iframe loads to fix zoom issues
    if (iframeRef.current) {
      // Use requestAnimationFrame to ensure DOM is ready
      requestAnimationFrame(() => {
        // Add a small delay to allow iframe content to fully render
        setTimeout(() => {
          if (iframeRef.current) {
            // Force layout recalculation by reading layout properties
            const iframe = iframeRef.current;
            iframe.offsetHeight; // Force reflow
            iframe.offsetWidth;  // Force reflow

            // Trigger a resize event on the window to ensure any resize handlers are called
            window.dispatchEvent(new Event('resize'));

            // Also try to trigger a resize on the iframe's parent container
            const container = iframe.parentElement;
            if (container) {
              container.offsetHeight; // Force reflow on container
              container.offsetWidth;  // Force reflow on container
            }

            // Additional fallback: trigger another layout recalculation after a longer delay
            // This helps with cases where the iframe content takes longer to fully initialize
            setTimeout(() => {
              if (iframeRef.current) {
                iframeRef.current.offsetHeight; // Force another reflow
                window.dispatchEvent(new Event('resize'));
              }
            }, 500);
          }
        }, 100); // Small delay to allow iframe content to settle
      });
    }
  }, []);

  return (
    <div className="h-full flex flex-col bg-gray-100 font-tahoma text-xs">
      <div className="p-1 bg-gray-200 border-b border-gray-300 flex items-center space-x-1 shrink-0">
        <span className="text-gray-500 font-semibold px-2">Address</span>
        <div className="flex-1 bg-white border border-gray-400 p-0.5 flex items-center">
          <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACLSURBVDhPY2AY6OLiAjo5Of+P4CIgvwHif4z/DAyM6Asa3AyMDCysDEyWpCAg/w9iDAwMjAycgCof4x8YGBgYWBgYjAkZGBgYGFgYWEzAGBj8//9/BgaE/zMy/P9/YGAQZWBgYGD4//+fmYGBgYFB5f/l/7GNgYGBgYHh//n/dUYGBgYGDADGABo4iP2XAAAAAElFTkSuQmCC" alt="ie icon" className="h-4 w-4 mr-1" />
          <input type="text" readOnly value="file:///C:/Users/<USER>/Documents/my_resume.pdf" className="w-full bg-transparent outline-none" />
        </div>
        <button className="xp-button px-2">Go</button>
      </div>
      <div className="flex-1 bg-white overflow-hidden">
        <iframe
          ref={iframeRef}
          src="https://drive.google.com/file/d/1XaY0tfH38EOgMk20P-qi3wWz_ZqGTi64/preview?rm=minimal&embedded=true"
          className="w-full h-full"
          style={{ border: 0 }}
          onLoad={handleIframeLoad}
        ></iframe>
      </div>
      <div className="p-2 bg-gray-100 border-t border-gray-300 flex justify-end items-center shrink-0">
        <div className="flex items-center space-x-2">
          <a href="https://drive.google.com/uc?export=download&id=1XaY0tfH38EOgMk20P-qi3wWz_ZqGTi64" download="my_resume.pdf" target="_blank" className="xp-button px-4 py-1 flex items-center space-x-2 no-underline text-black">
            <Download size={16} />
            <span>Download Resume</span>
          </a>
        </div>
      </div>
    </div>
  );
};

export default ResumeContent;
