import React from 'react';
import RecycleBinContent from '../components/windows/RecycleBinContent';
import SkillsContent from '../components/windows/SkillsContent';
import MyComputerContent from '../components/windows/MyComputerContent';
import MyDocumentsContent from '../components/windows/MyDocumentsContent';
import MyPicturesContent from '../components/windows/MyPicturesContent';
import MyMusicContent from '../components/windows/MyMusicContent';
import ControlPanelContent from '../components/windows/ControlPanelContent';
import ResumeContent from '../components/windows/ResumeContent';
import ContactContent from '../components/windows/ContactContent';
import ProjectFolder from '../components/ProjectFolder';
import ProjectViewer from '../components/windows/ProjectViewer';

type OpenWindowFunc = (id: string, name: string) => void;
type UpdateWindowSizeFunc = (windowId: string, size: { width: number; height: number }) => void;

interface WindowContentOptions {
  onOpenWindow: OpenWindowFunc;
  updateWindowSize?: UpdateWindowSizeFunc;
  windowId?: string;
}

export const getWindowContent = (iconId: string, options: OpenWindowFunc | WindowContentOptions): React.ReactNode => {
  // Handle backward compatibility - if options is a function, it's the old onOpenWindow parameter
  const onOpenWindow = typeof options === 'function' ? options : options.onOpenWindow;
  const updateWindowSize = typeof options === 'object' ? options.updateWindowSize : undefined;
  const windowId = typeof options === 'object' ? options.windowId : undefined;
  switch (iconId) {
    case 'recyclebin':
      return <RecycleBinContent />;
    case 'my_computer':
      return <MyComputerContent />;
    case 'skills':
      return <SkillsContent />;
    case 'my_documents':
      return <MyDocumentsContent onOpenWindow={onOpenWindow} />;
    case 'my_pictures':
      return <MyPicturesContent />;
    case 'my_music':
      return <MyMusicContent />;
    case 'control_panel':
      return <ControlPanelContent />;
    case 'certifications':
      return (
        <div className="p-4">
          <h2 className="text-lg font-bold mb-4">Certifications</h2>
          <p>This is a placeholder for certifications. I would list my professional certifications here, if I had any.</p>
          <ul className="list-disc list-inside mt-2">
            <li>Example: Awesome Developer Certificate</li>
            <li>Example: Certified Tech Guru</li>
          </ul>
        </div>
      );
    case 'projects':
      return (
        <div className="p-4 sm:p-6 bg-gradient-to-br from-gray-50 to-blue-50 min-h-full">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 max-w-5xl mx-auto justify-items-center">
            <ProjectFolder
              name="Zendo"
              description="Browser Extension"
              icon="🧘"
              onClick={() => onOpenWindow('project-zendo', 'Zendo - Browser Extension')}
            />
            <ProjectFolder
              name="Chromepanion"
              description="Browser Extension"
              icon="🤖"
              onClick={() => onOpenWindow('project-chromepanion', 'Chromepanion - Browser Extension')}
            />
            <ProjectFolder
              name="Moverzz"
              description="Rental Mobile App"
              icon="🚚"
              onClick={() => onOpenWindow('project-moverzz', 'Moverzz - Rental Mobile App')}
            />
            <ProjectFolder
              name="E-Commerce Platform"
              description="Full-stack Web App"
              icon="🛍️"
              onClick={() => onOpenWindow('project-ecommerce', 'E-Commerce Platform')}
            />
          </div>
          <div className="mt-8 text-center">
            <div className="inline-block bg-white/70 backdrop-blur-sm rounded-lg px-4 py-2 border border-blue-200">
              <p className="text-sm text-gray-600">
                💡 <strong>Tip:</strong> Click on any project folder to explore detailed information
              </p>
            </div>
          </div>
        </div>
      );
    case 'my_resume':
      return <ResumeContent updateWindowSize={updateWindowSize} windowId={windowId} />;
    case 'contact':
      return <ContactContent />;
    case 'about':
      return (
        <div className="p-4 bg-white font-mono text-sm">
          <div className="border-b pb-2 mb-4">
            <span className="font-bold">About Me.txt</span>
          </div>
          <div className="whitespace-pre-line">
            {`Hello! I'm John Smith, a passionate software engineer with over 5 years of experience building web applications and scalable systems.

What I Do:
- Full-stack web development
- Cloud architecture and DevOps
- Mobile app development
- System design and optimization

My Approach:
I believe in writing clean, maintainable code and creating solutions that truly serve users' needs. I'm always excited to learn new technologies and tackle challenging problems.

Current Focus:
- Microservices architecture
- AI/ML integration
- Performance optimization
- Team leadership and mentoring

When I'm not coding, you can find me:
- Contributing to open source projects
- Reading about emerging technologies
- Playing video games (yes, including retro ones!)
- Hiking and photography

Thanks for visiting my Windows XP themed portfolio! This nostalgic interface showcases my skills while paying homage to the golden age of computing. 

Feel free to explore my projects and get in touch if you'd like to collaborate!

---
Last modified: 2024
File size: 1.2 KB`}
          </div>
        </div>
      );
    case 'project-zendo':
      return <ProjectViewer projectId="zendo" onNavigateBack={() => onOpenWindow('projects', 'My Projects')} />;
    case 'project-chromepanion':
      return <ProjectViewer projectId="chromepanion" onNavigateBack={() => onOpenWindow('projects', 'My Projects')} />;
    case 'project-moverzz':
      return <ProjectViewer projectId="moverzz" onNavigateBack={() => onOpenWindow('projects', 'My Projects')} />;
    case 'project-ecommerce':
      return <ProjectViewer projectId="ecommerce" onNavigateBack={() => onOpenWindow('projects', 'My Projects')} />;
    default:
      return <div className="p-4">Window content not found.</div>;
  }
};
