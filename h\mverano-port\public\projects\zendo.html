<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zendo - Browser Extension</title>
    <style>
        body {
            font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: #316AC5;
            color: black;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #ECE9D8;
            padding: 20px;
            border: 2px outset #ECE9D8;
            box-shadow: 2px 2px 5px #808080;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 2em;
            color: #000080;
            text-decoration: underline;
        }
        .project-info {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .info-card {
            background: #F0F0F0;
            padding: 15px;
            border: 2px inset #ECE9D8;
            margin-bottom: 15px;
        }
        .tech-stack {
            margin-top: 10px;
        }
        .tech-tag {
            background: #DBEAFE;
            padding: 3px 8px;
            border: 1px solid #3B82F6;
            font-size: 0.8em;
            margin: 2px;
            display: inline-block;
        }
        .features {
            list-style: none;
            padding: 0;
            margin: 10px 0;
        }
        .features li {
            padding: 5px 0;
            border-bottom: 1px solid #C0C0C0;
        }
        .features li:before {
            content: "• ";
            color: #000080;
            font-weight: bold;
        }
        .back-btn {
            background: #ECE9D8;
            color: black;
            border: 2px outset #ECE9D8;
            padding: 5px 15px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 15px;
            font-family: Tahoma, Arial, sans-serif;
        }
        .back-btn:hover {
            background: #D4D0C8;
        }
        .chrome-store-btn {
            display: inline-block;
            background: #0066CC;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            font-weight: bold;
            border: 2px outset #0066CC;
            font-family: Tahoma, Arial, sans-serif;
        }
        .chrome-store-btn:hover {
            background: #0052A3;
            border: 2px inset #0066CC;
        }
        h3 {
            color: #000080;
            font-size: 1.1em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Zendo Browser Extension</h1>
        <p style="text-align: center; font-size: 1em; margin-bottom: 20px; font-weight: bold;">
            Minimalist to-do list with task grouping, drag-and-drop, and color priorities.
        </p>

        <!-- Screenshot Section -->
        <div style="text-align: center; margin-bottom: 20px;">
            <img src="/src/assets/browser-ext/z-task.png" alt="Zendo Extension Screenshot" style="max-width: 100%; height: auto; border: 2px inset #ECE9D8;">
        </div>
        
        <div class="project-info">
            <div class="info-card">
                <h3>🛠️ Technology Stack</h3>
                <div class="tech-stack">
                    <span class="tech-tag">JavaScript</span>
                    <span class="tech-tag">Chrome Extension API</span>
                    <span class="tech-tag">HTML5</span>
                    <span class="tech-tag">CSS3</span>
                    <span class="tech-tag">Chrome Sync</span>
                </div>
            </div>

            <div class="info-card">
                <h3>📊 Project Status</h3>
                <p><strong>Status:</strong> Published</p>
                <p><strong>Version:</strong> Latest</p>
                <p><strong>Platform:</strong> Chrome Web Store</p>
                <p><strong>Offline:</strong> Fully supported</p>
            </div>
        </div>

        <!-- Chrome Web Store Link -->
        <div style="text-align: center; margin-bottom: 30px;">
            <a href="https://chromewebstore.google.com/detail/zendo/docecfpjajpjlfpidlbmdodgaopdgdfo"
               target="_blank"
               class="chrome-store-btn">
                🚀 Install from Chrome Web Store
            </a>
        </div>
        
        <div class="info-card">
            <h3>✨ Key Features</h3>
            <ul class="features">
                <li>Grouped Tasks – Organize tasks by project, context, or theme</li>
                <li>Drag & Drop – Easily reorder tasks and groups with intuitive gestures</li>
                <li>Custom Priorities – Choose and personalize priority levels using color tags</li>
                <li>Zen-Inspired Design – Clean, minimal UI with Apple-style visual polish</li>
                <li>One-Click Complete & Edit – Click to check off tasks or edit inline</li>
                <li>Chrome Sync – Store tasks and settings across devices</li>
            </ul>
        </div>
        
        <div class="info-card">
            <h3>📝 Description</h3>
            <p>
                <strong>Zendo – Grouped To-Do List & Task Manager for Chrome</strong><br>
                Zendo is a minimalist to-do list extension for Chrome that helps you organize tasks into groups,
                set color-coded priorities, and stay focused with a clean, drag-and-drop interface.
            </p>
            <p>
                <strong>Zendo is ideal for:</strong><br>
                • Daily task planning<br>
                • Project-based to-do management<br>
                • Students, creatives, and professionals seeking simplicity<br>
                • Anyone who prefers grouped over nested tasks
            </p>
            <p>
                Works fully offline and uses Chrome Sync to store your tasks and settings across devices.<br>
                <strong>No login. No tracking. No distractions.</strong>
            </p>
        </div>
        
        <a href="javascript:history.back()" class="back-btn">← Back to Projects</a>
    </div>
</body>
</html>
